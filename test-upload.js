// Simple test script to verify image upload endpoint
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function testImageUpload() {
  try {
    // Create a simple test image (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x5C, 0xC2, 0x5D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    // Create form data
    const formData = new FormData();
    formData.append('image', testImageBuffer, {
      filename: 'test.png',
      contentType: 'image/png'
    });
    formData.append('userId', '999'); // Test user ID

    // Send request to upload endpoint
    const response = await axios.post('http://localhost:3000/user/upload-image', formData, {
      headers: {
        ...formData.getHeaders()
      }
    });

    console.log('Upload successful:', response.data);
    
    // Test getting the image back
    const imageResponse = await axios.get('http://localhost:3000/user/image/999');
    console.log('Image retrieval successful:', imageResponse.data);

  } catch (error) {
    console.error('Test failed:', error.response?.data || error.message);
  }
}

// Run the test
testImageUpload();
