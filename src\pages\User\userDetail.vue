<template>
  <UserNavigation></UserNavigation>
  <div class="container q-pa-md q-ma-md">
    <div class="row justify-between q-mb-md">
      <q-btn icon="arrow_back" color="grey-7" flat @click="goBack" class="back-btn" />
    </div>
    <div class="user-detail-card">
      <div class="row justify-center">
        <div class="col-12 col-md-6 flex justify-center">
          <div class="user-image-container">
            <img :src="userImage" alt="User profile" class="user-image" />
          </div>
        </div>
        <div class="col-12 col-md-6 flex items-center">
          <div class="user-info-list">
            <div class="info-item">
              <div class="info-icon"><q-icon name="badge" size="24px" color="grey-7" /></div>
              <div class="info-label">ID</div>
              <div class="info-value">{{ userData?.id || '0000' }}</div>
            </div>
            <div class="info-item">
              <div class="info-icon"><q-icon name="person" size="24px" color="grey-7" /></div>
              <div class="info-label">ชื่อ นามสกุล</div>
              <div class="info-value">{{ userData?.name || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="info-icon"><q-icon name="phone" size="24px" color="grey-7" /></div>
              <div class="info-label">เบอร์โทร</div>
              <div class="info-value">{{ userData?.tel || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="info-icon"><q-icon name="work" size="24px" color="grey-7" /></div>
              <div class="info-label">ตำแหน่ง</div>
              <div class="info-value">{{ userData?.role || 'ประจำ' }}</div>
            </div>
            <div class="info-item">
              <div class="info-icon"><q-icon name="store" size="24px" color="grey-7" /></div>
              <div class="info-label">สาขา</div>
              <div class="info-value">{{ userData?.branch?.name || 'บางแสน' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="action-buttons">
      <div class="row justify-center q-col-gutter-md">
        <div class="col-3 text-center">
          <q-btn
            round
            flat
            color="white"
            size="lg"
            icon="event"
            @click="goToLeaveRequest"
            class="bg-main"
          />
          <div class="button-label">ปฏิทิน</div>
        </div>
        <div class="col-3 text-center">
          <q-btn
            round
            flat
            color="white"
            size="lg"
            icon="description"
            class="bg-main"
            @click="goToAttendanceDetail"
          />
          <div class="button-label">รายงานการทำงาน</div>
        </div>
        <div class="col-3 text-center">
          <q-btn
            round
            flat
            color="white"
            size="lg"
            icon="edit"
            class="bg-yellow"
            @click="openEditDialog"
          />
          <div class="button-label">แก้ไขข้อมูล</div>
        </div>
        <div class="col-3 text-center">
          <q-btn
            round
            flat
            color="white"
            size="lg"
            icon="delete"
            class="bg-red"
            @click="openDeleteDialog"
          />
          <div class="button-label">ลบข้อมูล</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Add User Dialog for editing -->
  <addUserDialog @user-updated="handleUserUpdated"></addUserDialog>

  <!-- Confirm Delete User Dialog -->
  <confirmDeleteUserDialog
    v-model="showDeleteDialog"
    :item="userData"
    @confirm="handleDeleteUser"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Notify } from 'quasar'
import UserNavigation from 'src/components/userNavigation.vue'
import addUserDialog from 'src/components/dialog/addUserDialog.vue'
import confirmDeleteUserDialog from 'src/components/dialog/confirmDeleteUserDialog.vue'
import { UserService } from 'src/services/userService'
import { useUserStore } from 'src/stores/userStore'
import { useUserDialogStore } from 'src/stores/dialog-user'
import type { user } from 'src/types/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const dialogStore = useUserDialogStore()
const userId = ref(Number(route.params.id))
const userData = ref<user | null>(null)
const userImage = ref('https://cdn.quasar.dev/img/avatar.png')
const showDeleteDialog = ref(false)

onMounted(async () => {
  await loadUserData()
})

// Watch for changes in the user store to update local userData
watch(
  () => userStore.users,
  (newUsers) => {
    if (newUsers && newUsers.length > 0) {
      const updatedUser = newUsers.find((u) => u.id === userId.value)
      if (updatedUser && userData.value) {
        // Update local userData with the latest data from store
        userData.value = { ...updatedUser }
        // Also update the user image if it changed
        void loadUserImage()
      }
    }
  },
  { deep: true },
)

// Watch for dialog close to refresh user data
watch(
  () => dialogStore.isOpen,
  (isOpen, wasOpen) => {
    // When dialog closes after being open (edit operation completed)
    if (!isOpen && wasOpen && dialogStore.mode === 'edit') {
      // Refresh user data to reflect any changes
      void refreshUserData()
    }
  },
)

const goBack = async () => {
  await router.push('/user/management')
}

const goToAttendanceDetail = async () => {
  await router.push({
    name: 'user-attendance-detail',
    params: { id: userId.value },
  })
}

const goToLeaveRequest = async () => {
  await router.push({
    name: 'user-leave-request',
    params: { id: userId.value },
  })
}

const loadUserData = async () => {
  try {
    const user = userStore.users.find((u) => u.id === userId.value)
    if (user) {
      userData.value = user
    } else {
      await userStore.fetchUsers()
      const fetchedUser = userStore.users.find((u) => u.id === userId.value)
      if (fetchedUser) {
        userData.value = fetchedUser
      }
    }

    await loadUserImage()
  } catch (error) {
    console.error('Error fetching user data:', error)
  }
}

const loadUserImage = async () => {
  if (userData.value?.id) {
    try {
      const imageUrl = await UserService.getUserImageById(userData.value.id)
      if (imageUrl) {
        userImage.value = imageUrl
      }
    } catch (error) {
      console.error('Error loading user image:', error)
    }
  }
}

const refreshUserData = async () => {
  try {
    // Force refresh from the store
    await userStore.fetchUsers()
    const updatedUser = userStore.users.find((u) => u.id === userId.value)
    if (updatedUser) {
      userData.value = { ...updatedUser }
      await loadUserImage()
    }
  } catch (error) {
    console.error('Error refreshing user data:', error)
  }
}

const openEditDialog = () => {
  if (userData.value) {
    // Set the current user data for editing using direct assignment
    userStore.editingUser = userData.value
    // Open dialog in edit mode
    dialogStore.open('edit')
  }
}

const handleUserUpdated = (updatedUser: user) => {
  // Immediately update the local userData with the updated user
  userData.value = { ...updatedUser }
  // Also refresh the user image in case it was updated
  void loadUserImage()
}

const openDeleteDialog = () => {
  showDeleteDialog.value = true
}

const handleDeleteUser = async (userToDelete: user | null) => {
  if (!userToDelete) {
    console.error('No user to delete')
    return
  }

  try {
    // Close the dialog first
    showDeleteDialog.value = false

    // Call the delete API
    await userStore.deleteUser(userToDelete.id)

    // Show success notification
    Notify.create({
      type: 'positive',
      message: 'ลบข้อมูลพนักงานเรียบร้อยแล้ว',
      position: 'top',
    })

    // Navigate back to user management page
    await router.push('/user/management')
  } catch (error) {
    console.error('Error deleting user:', error)

    // Close the dialog even on error
    showDeleteDialog.value = false

    // Show error notification
    Notify.create({
      type: 'negative',
      message: 'เกิดข้อผิดพลาดในการลบข้อมูลพนักงาน',
      position: 'top',
    })
  }
}
</script>

<style scoped>
.user-detail-card {
  background-color: #e1edea;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 30px;
}

.user-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  height: 100%;
}

.user-image {
  max-width: 250px;
  max-height: 300px;
  border-radius: 5px;
  object-fit: cover;
}

.user-info-list {
  padding: 10px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
}

.info-icon {
  width: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.info-label {
  width: 120px;
  font-weight: bold;
  margin-right: 20px;
}

.info-value {
  flex: 1;
  font-size: 16px;
}

.action-buttons {
  margin-top: 30px;
}

.button-label {
  margin-top: 8px;
  font-size: 14px;
}

@media (max-width: 768px) {
  .user-image-container {
    margin-bottom: 20px;
  }

  .info-item {
    flex-wrap: wrap;
  }

  .info-label {
    width: calc(100% - 40px);
    margin-bottom: 5px;
  }

  .info-value {
    width: 100%;
    padding-left: 40px;
  }
}
</style>
